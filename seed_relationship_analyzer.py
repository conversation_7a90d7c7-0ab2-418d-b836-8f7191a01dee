#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
种子关系分析工具
分析开奖时间戳与随机数序列时间戳的关系，找出真实的种子生成规律
"""

import pandas as pd
import ast
import random
from typing import List, Dict, Tuple
from collections import Counter

class SeedRelationshipAnalyzer:
    """种子关系分析器"""
    
    def __init__(self, data_file: str = "real_time_data_20250817.csv"):
        self.data_file = data_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        print(f"📂 加载数据: {self.data_file}")
        self.data = pd.read_csv(self.data_file, encoding='utf-8')
        print(f"✅ 加载完成，共 {len(self.data)} 条记录")
    
    def parse_timestamp_list(self, timestamp_str: str) -> List[int]:
        """解析时间戳列表"""
        try:
            if pd.isna(timestamp_str):
                return []
            return ast.literal_eval(str(timestamp_str))
        except:
            return []
    
    def unity_random(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def analyze_timestamp_relationships(self) -> Dict:
        """分析时间戳关系"""
        print("\n🔍 分析开奖时间戳与随机数序列时间戳的关系...")
        
        relationships = []
        
        for _, row in self.data.iterrows():
            try:
                # 获取开奖时间戳
                draw_timestamp = int(row['开奖时间戳'])
                
                # 解析随机数序列时间戳
                sequence_timestamps = self.parse_timestamp_list(row['随机数序列对应的时间戳'])
                
                if not sequence_timestamps:
                    continue
                
                # 分析关系
                first_seed = sequence_timestamps[0]
                last_seed = sequence_timestamps[-1]
                
                # 计算偏移量
                offset_to_first = first_seed - draw_timestamp
                offset_to_last = last_seed - draw_timestamp
                
                relationships.append({
                    'period': row['期号'],
                    'draw_timestamp': draw_timestamp,
                    'first_seed': first_seed,
                    'last_seed': last_seed,
                    'sequence_length': len(sequence_timestamps),
                    'offset_to_first': offset_to_first,
                    'offset_to_last': offset_to_last,
                    'actual_result': int(row['开出的房间'])
                })
                
            except Exception as e:
                print(f"⚠️ 处理期号 {row.get('期号', 'unknown')} 失败: {e}")
                continue
        
        return relationships
    
    def find_true_seed_pattern(self, relationships: List[Dict]) -> Dict:
        """寻找真实种子模式"""
        print("\n🔬 寻找真实种子生成模式...")
        
        # 统计偏移量模式
        first_offsets = [r['offset_to_first'] for r in relationships]
        last_offsets = [r['offset_to_last'] for r in relationships]
        
        first_offset_counter = Counter(first_offsets)
        last_offset_counter = Counter(last_offsets)
        
        # 分析序列长度
        sequence_lengths = [r['sequence_length'] for r in relationships]
        length_counter = Counter(sequence_lengths)
        
        # 寻找真实种子
        true_seed_candidates = []
        
        for rel in relationships:
            draw_ts = rel['draw_timestamp']
            actual_result = rel['actual_result']
            
            # 测试不同的偏移量
            for offset in range(-1000, 1001):  # 测试±1000的偏移
                test_seed = draw_ts + offset
                predicted = self.unity_random(test_seed)
                
                if predicted == actual_result:
                    true_seed_candidates.append({
                        'period': rel['period'],
                        'draw_timestamp': draw_ts,
                        'true_seed': test_seed,
                        'offset': offset,
                        'predicted': predicted,
                        'actual': actual_result
                    })
        
        # 统计真实种子的偏移模式
        true_offsets = [c['offset'] for c in true_seed_candidates]
        true_offset_counter = Counter(true_offsets)
        
        return {
            'relationships': relationships,
            'first_offset_patterns': first_offset_counter.most_common(10),
            'last_offset_patterns': last_offset_counter.most_common(10),
            'sequence_length_patterns': length_counter.most_common(5),
            'true_seed_candidates': true_seed_candidates,
            'true_offset_patterns': true_offset_counter.most_common(10),
            'statistics': {
                'total_records': len(relationships),
                'true_seed_matches': len(true_seed_candidates),
                'unique_periods_with_true_seeds': len(set(c['period'] for c in true_seed_candidates))
            }
        }
    
    def analyze_sequence_vs_true_seeds(self, pattern_analysis: Dict) -> Dict:
        """分析序列种子与真实种子的关系"""
        print("\n🎯 分析随机数序列种子与真实种子的关系...")
        
        comparison = {
            'sequence_seed_analysis': {},
            'true_seed_analysis': {},
            'relationship_findings': []
        }
        
        # 分析序列中的种子
        for rel in pattern_analysis['relationships']:
            period = rel['period']
            draw_ts = rel['draw_timestamp']
            actual_result = rel['actual_result']
            
            # 获取对应的随机数序列
            row = self.data[self.data['期号'] == period].iloc[0]
            sequence_timestamps = self.parse_timestamp_list(row['随机数序列对应的时间戳'])
            
            if not sequence_timestamps:
                continue
            
            # 测试序列中每个种子
            sequence_predictions = []
            for i, seed in enumerate(sequence_timestamps):
                predicted = self.unity_random(seed)
                sequence_predictions.append({
                    'index': i,
                    'seed': seed,
                    'predicted': predicted,
                    'matches_actual': predicted == actual_result
                })
            
            # 找出匹配实际结果的种子
            matching_seeds = [p for p in sequence_predictions if p['matches_actual']]
            
            comparison['sequence_seed_analysis'][period] = {
                'total_seeds': len(sequence_timestamps),
                'matching_seeds': matching_seeds,
                'matching_count': len(matching_seeds)
            }
        
        # 分析真实种子
        true_seeds_by_period = {}
        for candidate in pattern_analysis['true_seed_candidates']:
            period = candidate['period']
            if period not in true_seeds_by_period:
                true_seeds_by_period[period] = []
            true_seeds_by_period[period].append(candidate)
        
        comparison['true_seed_analysis'] = true_seeds_by_period
        
        # 比较分析
        for period in comparison['sequence_seed_analysis']:
            seq_analysis = comparison['sequence_seed_analysis'][period]
            true_analysis = true_seeds_by_period.get(period, [])
            
            if seq_analysis['matching_seeds'] and true_analysis:
                # 检查是否有重叠
                seq_seeds = set(s['seed'] for s in seq_analysis['matching_seeds'])
                true_seeds = set(t['true_seed'] for t in true_analysis)
                
                overlap = seq_seeds & true_seeds
                
                comparison['relationship_findings'].append({
                    'period': period,
                    'sequence_matching_seeds': list(seq_seeds),
                    'true_seeds': list(true_seeds),
                    'overlap': list(overlap),
                    'has_overlap': len(overlap) > 0
                })
        
        return comparison
    
    def generate_comprehensive_report(self, pattern_analysis: Dict, comparison: Dict) -> str:
        """生成综合分析报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("🔬 种子关系综合分析报告")
        lines.append("=" * 80)
        
        # 基本统计
        stats = pattern_analysis['statistics']
        lines.append(f"\n📊 基本统计:")
        lines.append(f"  • 分析记录数: {stats['total_records']}")
        lines.append(f"  • 找到真实种子的记录: {stats['true_seed_matches']}")
        lines.append(f"  • 涉及期号数: {stats['unique_periods_with_true_seeds']}")
        
        # 时间戳关系分析
        lines.append(f"\n🕐 开奖时间戳与序列时间戳关系:")
        lines.append("  序列首个时间戳偏移量 (前5个):")
        for offset, count in pattern_analysis['first_offset_patterns'][:5]:
            lines.append(f"    • 偏移 {offset}: {count} 次")
        
        lines.append("  序列最后时间戳偏移量 (前5个):")
        for offset, count in pattern_analysis['last_offset_patterns'][:5]:
            lines.append(f"    • 偏移 {offset}: {count} 次")
        
        # 真实种子分析
        lines.append(f"\n🎯 真实种子偏移模式 (前10个):")
        if pattern_analysis['true_offset_patterns']:
            for offset, count in pattern_analysis['true_offset_patterns']:
                lines.append(f"  • 开奖时间戳 + {offset}: {count} 次命中")
        else:
            lines.append("  未找到明确的真实种子偏移模式")
        
        # 序列长度分析
        lines.append(f"\n📏 随机数序列长度分析:")
        for length, count in pattern_analysis['sequence_length_patterns']:
            lines.append(f"  • 长度 {length}: {count} 次")
        
        # 关键发现
        lines.append(f"\n💡 关键发现:")
        
        # 1. 真实种子规律
        if pattern_analysis['true_offset_patterns']:
            most_common_offset = pattern_analysis['true_offset_patterns'][0]
            lines.append(f"  1. 最可能的真实种子生成规律:")
            lines.append(f"     真实种子 = 开奖时间戳 + {most_common_offset[0]}")
            lines.append(f"     (在 {most_common_offset[1]} 次测试中命中)")
        
        # 2. 序列种子与真实种子关系
        overlap_count = sum(1 for f in comparison['relationship_findings'] if f['has_overlap'])
        total_findings = len(comparison['relationship_findings'])
        if total_findings > 0:
            lines.append(f"  2. 序列种子与真实种子重叠情况:")
            lines.append(f"     {overlap_count}/{total_findings} 个期号存在重叠")
            lines.append(f"     重叠率: {overlap_count/total_findings:.1%}")
        
        # 3. 实用建议
        lines.append(f"  3. 实用建议:")
        if pattern_analysis['true_offset_patterns']:
            best_offset = pattern_analysis['true_offset_patterns'][0][0]
            lines.append(f"     • 使用 '开奖时间戳 + {best_offset}' 作为真实种子")
            lines.append(f"     • 该种子生成的随机数即为开奖结果")
            lines.append(f"     • 避开该随机数可提高获胜概率")
        else:
            lines.append(f"     • 继续使用现有的滑动窗口区域方法")
            lines.append(f"     • 真实种子模式可能更复杂，需要更多数据")
        
        lines.append("=" * 80)
        return "\n".join(lines)
    
    def run_complete_analysis(self) -> Dict:
        """运行完整分析"""
        print("🚀 开始种子关系综合分析...")
        
        # 分析时间戳关系
        relationships = self.analyze_timestamp_relationships()
        
        # 寻找真实种子模式
        pattern_analysis = self.find_true_seed_pattern(relationships)
        
        # 分析序列种子与真实种子关系
        comparison = self.analyze_sequence_vs_true_seeds(pattern_analysis)
        
        # 生成报告
        report = self.generate_comprehensive_report(pattern_analysis, comparison)
        print(report)
        
        return {
            'pattern_analysis': pattern_analysis,
            'comparison': comparison,
            'report': report
        }

def main():
    """主函数"""
    analyzer = SeedRelationshipAnalyzer("real_time_data_20250817.csv")
    result = analyzer.run_complete_analysis()
    
    # 保存结果
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"seed_relationship_analysis_{timestamp}.json"
    
    # 保存可序列化的结果
    serializable_result = {
        'report': result['report'],
        'statistics': result['pattern_analysis']['statistics'],
        'true_offset_patterns': result['pattern_analysis']['true_offset_patterns'],
        'first_offset_patterns': result['pattern_analysis']['first_offset_patterns'][:10],
        'sequence_length_patterns': result['pattern_analysis']['sequence_length_patterns']
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(serializable_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 分析结果已保存到: {filename}")

if __name__ == "__main__":
    main()
