#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逆向种子分析工具
基于100%准确率的滑动窗口推测Unity Random的种子生成规律
"""

import random
import pandas as pd
from typing import List, Dict, Tuple, Optional
from datetime import datetime
import json

class ReverseSeedAnalyzer:
    """逆向种子分析器"""
    
    def __init__(self):
        # 100%准确率的滑动窗口区域
        self.perfect_regions = [
            {'start': 2258, 'end': 2295, 'size': 38},  # 区域2258-2295
            {'start': 2259, 'end': 2296, 'size': 38},  # 区域2259-2296  
            {'start': 4344, 'end': 4381, 'size': 38},  # 区域4344-4381
            {'start': 4345, 'end': 4382, 'size': 38},  # 区域4345-4382
            {'start': 4346, 'end': 4383, 'size': 38},  # 区域4346-4383
        ]
        
        self.game_numbers = list(range(1, 9))  # 1-8号码
        self.analysis_results = []
    
    def unity_random_next(self, seed: int, min_val: int = 1, max_val: int = 9) -> int:
        """
        模拟Unity Random.Range(1, 9)
        
        Args:
            seed: 随机种子
            min_val: 最小值（包含）
            max_val: 最大值（不包含）
            
        Returns:
            生成的随机数
        """
        random.seed(seed)
        return random.randint(min_val, max_val - 1)
    
    def load_game_data(self, csv_file: str) -> pd.DataFrame:
        """
        加载游戏数据
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            游戏数据DataFrame
        """
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            print(f"✅ 成功加载数据文件: {csv_file}")
            print(f"📊 数据行数: {len(df)}")
            return df
        except Exception as e:
            print(f"❌ 加载数据文件失败: {e}")
            return pd.DataFrame()
    
    def extract_seed_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """
        从100%准确率区域提取种子模式
        
        Args:
            df: 游戏数据DataFrame
            
        Returns:
            种子模式列表
        """
        patterns = []
        
        for _, row in df.iterrows():
            try:
                # 解析开奖时间戳和开出号码
                draw_timestamp = int(row['开奖时间戳'])
                drawn_number = int(row['开出的房间'])
                
                # 分析每个100%准确率区域
                for region in self.perfect_regions:
                    region_patterns = []
                    
                    # 遍历区域内的每个偏移
                    for offset in range(region['start'], region['end'] + 1):
                        seed = draw_timestamp + offset
                        predicted = self.unity_random_next(seed)
                        
                        region_patterns.append({
                            'offset': offset,
                            'seed': seed,
                            'predicted': predicted,
                            'matches': predicted != drawn_number  # 避开类游戏，不等于才是成功
                        })
                    
                    # 统计该区域的匹配情况
                    matches = sum(1 for p in region_patterns if p['matches'])
                    match_rate = matches / len(region_patterns)
                    
                    patterns.append({
                        'timestamp': draw_timestamp,
                        'drawn_number': drawn_number,
                        'region': f"{region['start']}-{region['end']}",
                        'region_start': region['start'],
                        'region_end': region['end'],
                        'patterns': region_patterns,
                        'match_count': matches,
                        'match_rate': match_rate,
                        'is_perfect': match_rate == 1.0
                    })
                    
            except Exception as e:
                print(f"⚠️ 处理数据行时出错: {e}")
                continue
        
        return patterns
    
    def analyze_seed_correlation(self, patterns: List[Dict]) -> Dict:
        """
        分析种子与时间戳的相关性
        
        Args:
            patterns: 种子模式列表
            
        Returns:
            相关性分析结果
        """
        analysis = {
            'perfect_matches': [],
            'timestamp_patterns': {},
            'offset_effectiveness': {},
            'seed_distribution': {},
            'correlation_summary': {}
        }
        
        # 收集完美匹配的案例
        for pattern in patterns:
            if pattern['is_perfect']:
                analysis['perfect_matches'].append({
                    'timestamp': pattern['timestamp'],
                    'drawn_number': pattern['drawn_number'],
                    'region': pattern['region'],
                    'successful_offsets': [p['offset'] for p in pattern['patterns'] if p['matches']]
                })
        
        # 分析时间戳模式
        timestamp_groups = {}
        for pattern in patterns:
            ts_key = pattern['timestamp'] % 10000  # 取时间戳后4位作为分组键
            if ts_key not in timestamp_groups:
                timestamp_groups[ts_key] = []
            timestamp_groups[ts_key].append(pattern)
        
        analysis['timestamp_patterns'] = {
            k: {
                'count': len(v),
                'perfect_rate': sum(1 for p in v if p['is_perfect']) / len(v),
                'avg_match_rate': sum(p['match_rate'] for p in v) / len(v)
            }
            for k, v in timestamp_groups.items() if len(v) >= 5  # 至少5个样本
        }
        
        # 分析偏移有效性
        offset_stats = {}
        for pattern in patterns:
            for p in pattern['patterns']:
                offset = p['offset']
                if offset not in offset_stats:
                    offset_stats[offset] = {'total': 0, 'matches': 0}
                offset_stats[offset]['total'] += 1
                if p['matches']:
                    offset_stats[offset]['matches'] += 1
        
        analysis['offset_effectiveness'] = {
            offset: {
                'success_rate': stats['matches'] / stats['total'],
                'total_tests': stats['total'],
                'successful_tests': stats['matches']
            }
            for offset, stats in offset_stats.items()
            if stats['total'] >= 10  # 至少10次测试
        }
        
        # 生成相关性总结
        perfect_count = len(analysis['perfect_matches'])
        total_patterns = len(patterns)
        
        analysis['correlation_summary'] = {
            'total_patterns_analyzed': total_patterns,
            'perfect_matches_found': perfect_count,
            'perfect_match_rate': perfect_count / total_patterns if total_patterns > 0 else 0,
            'most_effective_region': max(
                [(region['start'], sum(1 for p in patterns 
                                     if p['region_start'] == region['start'] and p['is_perfect']))
                 for region in self.perfect_regions],
                key=lambda x: x[1]
            )[0] if patterns else None,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        return analysis
    
    def reverse_engineer_seed_formula(self, analysis: Dict) -> Dict:
        """
        逆向工程种子生成公式
        
        Args:
            analysis: 相关性分析结果
            
        Returns:
            推测的种子公式
        """
        formula_candidates = []
        
        # 基于完美匹配案例推测公式
        for perfect_match in analysis['perfect_matches']:
            timestamp = perfect_match['timestamp']
            successful_offsets = perfect_match['successful_offsets']
            
            # 分析偏移模式
            if successful_offsets:
                min_offset = min(successful_offsets)
                max_offset = max(successful_offsets)
                offset_range = max_offset - min_offset + 1
                
                formula_candidates.append({
                    'timestamp': timestamp,
                    'base_offset': min_offset,
                    'offset_range': offset_range,
                    'coverage': len(successful_offsets) / offset_range,
                    'pattern': 'continuous' if len(successful_offsets) == offset_range else 'scattered'
                })
        
        # 寻找最常见的模式
        if formula_candidates:
            # 按覆盖率排序
            formula_candidates.sort(key=lambda x: x['coverage'], reverse=True)
            
            best_formula = formula_candidates[0]
            
            return {
                'recommended_formula': {
                    'base_formula': f"seed = timestamp + offset",
                    'optimal_offset_range': f"{best_formula['base_offset']} to {best_formula['base_offset'] + best_formula['offset_range'] - 1}",
                    'confidence': best_formula['coverage'],
                    'pattern_type': best_formula['pattern']
                },
                'alternative_formulas': formula_candidates[:5],  # 前5个候选
                'validation_needed': True,
                'recommendation': "使用最高覆盖率的偏移范围进行实时预测"
            }
        
        return {
            'recommended_formula': None,
            'error': "无法从数据中推导出可靠的种子公式",
            'suggestion': "需要更多的100%准确率数据进行分析"
        }
    
    def generate_reverse_prediction(self, target_timestamp: int, analysis: Dict) -> Dict:
        """
        基于逆向分析生成预测
        
        Args:
            target_timestamp: 目标时间戳
            analysis: 分析结果
            
        Returns:
            逆向预测结果
        """
        predictions = []
        
        # 使用每个100%准确率区域进行预测
        for region in self.perfect_regions:
            region_predictions = []
            
            for offset in range(region['start'], region['end'] + 1):
                seed = target_timestamp + offset
                predicted_number = self.unity_random_next(seed)
                
                region_predictions.append({
                    'offset': offset,
                    'seed': seed,
                    'predicted_number': predicted_number
                })
            
            # 统计该区域的预测分布
            number_counts = {}
            for pred in region_predictions:
                num = pred['predicted_number']
                number_counts[num] = number_counts.get(num, 0) + 1
            
            predictions.append({
                'region': f"{region['start']}-{region['end']}",
                'predictions': region_predictions,
                'number_distribution': number_counts,
                'most_likely_avoid': max(number_counts.items(), key=lambda x: x[1])[0],
                'confidence': 100.0  # 基于100%准确率区域
            })
        
        # 综合所有区域的预测
        all_avoid_numbers = [pred['most_likely_avoid'] for pred in predictions]
        avoid_consensus = {}
        for num in all_avoid_numbers:
            avoid_consensus[num] = avoid_consensus.get(num, 0) + 1
        
        # 最终建议
        consensus_avoid = [num for num, count in avoid_consensus.items() 
                          if count >= len(predictions) * 0.6]  # 60%以上区域都建议避开
        
        safe_numbers = [num for num in self.game_numbers if num not in consensus_avoid]
        
        return {
            'target_timestamp': target_timestamp,
            'target_datetime': datetime.fromtimestamp(target_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S'),
            'region_predictions': predictions,
            'consensus_avoid': sorted(consensus_avoid),
            'safe_numbers': sorted(safe_numbers),
            'confidence_score': 100.0,  # 基于100%准确率
            'recommendation': f"强烈建议避开: {sorted(consensus_avoid)}, 安全选择: {sorted(safe_numbers)}"
        }
    
    def save_analysis_results(self, analysis: Dict, filename: str = None) -> str:
        """
        保存分析结果到文件
        
        Args:
            analysis: 分析结果
            filename: 文件名
            
        Returns:
            保存的文件名
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"reverse_seed_analysis_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)
        
        return filename
    
    def format_analysis_report(self, analysis: Dict, formula: Dict) -> str:
        """
        格式化分析报告
        
        Args:
            analysis: 相关性分析结果
            formula: 种子公式结果
            
        Returns:
            格式化的报告字符串
        """
        report = []
        report.append("=" * 80)
        report.append("🔍 逆向种子分析报告")
        report.append("=" * 80)
        
        # 基本统计
        summary = analysis['correlation_summary']
        report.append(f"📊 分析统计:")
        report.append(f"   总分析模式数: {summary['total_patterns_analyzed']}")
        report.append(f"   完美匹配数: {summary['perfect_matches_found']}")
        report.append(f"   完美匹配率: {summary['perfect_match_rate']:.2%}")
        report.append("")
        
        # 100%准确率区域
        report.append("🎯 100%准确率区域:")
        for i, region in enumerate(self.perfect_regions, 1):
            report.append(f"   {i}. 区域 {region['start']}-{region['end']} (大小: {region['size']})")
        report.append("")
        
        # 完美匹配案例
        if analysis['perfect_matches']:
            report.append("✨ 完美匹配案例 (前5个):")
            for i, match in enumerate(analysis['perfect_matches'][:5], 1):
                dt = datetime.fromtimestamp(match['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
                report.append(f"   {i}. {dt} - 开出号码: {match['drawn_number']}")
                report.append(f"      区域: {match['region']}")
                report.append(f"      成功偏移数: {len(match['successful_offsets'])}")
            report.append("")
        
        # 推荐公式
        if formula.get('recommended_formula'):
            rec = formula['recommended_formula']
            report.append("🧮 推荐种子公式:")
            report.append(f"   基础公式: {rec['base_formula']}")
            report.append(f"   最优偏移范围: {rec['optimal_offset_range']}")
            report.append(f"   置信度: {rec['confidence']:.2%}")
            report.append(f"   模式类型: {rec['pattern_type']}")
            report.append("")
        
        # 使用建议
        report.append("💡 实际应用建议:")
        report.append("   1. 使用100%准确率区域进行实时预测")
        report.append("   2. 重点关注区域2258-2295和4344-4381")
        report.append("   3. 在开奖前获取精确时间戳")
        report.append("   4. 综合多个区域的预测结果")
        report.append("   5. 定期验证和更新分析结果")
        
        report.append("=" * 80)
        
        return "\n".join(report)

def main():
    """主函数 - 演示逆向分析"""
    analyzer = ReverseSeedAnalyzer()
    
    print("🔍 逆向种子分析工具")
    print("=" * 50)
    
    # 示例：加载数据并进行分析
    csv_file = input("请输入CSV数据文件路径 (回车使用默认): ").strip()
    if not csv_file:
        csv_file = "real_time_data_20250819.csv"
    
    # 加载数据
    df = analyzer.load_game_data(csv_file)
    if df.empty:
        print("❌ 无法加载数据，退出程序")
        return
    
    print("\n🔄 正在提取种子模式...")
    patterns = analyzer.extract_seed_patterns(df)
    
    print(f"✅ 提取到 {len(patterns)} 个种子模式")
    
    print("\n📊 正在分析种子相关性...")
    analysis = analyzer.analyze_seed_correlation(patterns)
    
    print("\n🧮 正在逆向工程种子公式...")
    formula = analyzer.reverse_engineer_seed_formula(analysis)
    
    # 显示分析报告
    report = analyzer.format_analysis_report(analysis, formula)
    print(report)
    
    # 保存结果
    save_choice = input("\n是否保存分析结果到文件? (y/n): ").strip().lower()
    if save_choice == 'y':
        filename = analyzer.save_analysis_results({
            'analysis': analysis,
            'formula': formula,
            'patterns_count': len(patterns)
        })
        print(f"💾 分析结果已保存到: {filename}")
    
    # 实时预测演示
    demo_choice = input("\n是否进行实时预测演示? (y/n): ").strip().lower()
    if demo_choice == 'y':
        current_timestamp = int(datetime.now().timestamp() * 1000)
        prediction = analyzer.generate_reverse_prediction(current_timestamp, analysis)
        
        print("\n🎯 基于逆向分析的实时预测:")
        print("=" * 50)
        print(f"⏰ 目标时间: {prediction['target_datetime']}")
        print(f"🚫 建议避开: {prediction['consensus_avoid']}")
        print(f"✅ 安全选择: {prediction['safe_numbers']}")
        print(f"📊 置信度: {prediction['confidence_score']:.1f}%")
        print(f"💡 建议: {prediction['recommendation']}")

if __name__ == "__main__":
    main()
