#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度模式分析工具
重新分析真实的随机数生成模式
"""

import pandas as pd
import random
import ast
from typing import List, Dict, Tuple
from collections import Counter, defaultdict

class DeepPatternAnalyzer:
    """深度模式分析器"""
    
    def __init__(self, data_file: str = "real_time_data_20250817.csv"):
        self.data_file = data_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        print(f"📂 加载数据: {self.data_file}")
        self.data = pd.read_csv(self.data_file, encoding='utf-8')
        print(f"✅ 加载完成，共 {len(self.data)} 条记录")
    
    def parse_timestamp_list(self, timestamp_str: str) -> List[int]:
        """解析时间戳列表"""
        try:
            if pd.isna(timestamp_str):
                return []
            return ast.literal_eval(str(timestamp_str))
        except:
            return []
    
    def unity_random(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def analyze_sequence_patterns(self) -> Dict:
        """分析随机数序列中的模式"""
        print("\n🔍 深度分析随机数序列模式...")
        
        analysis = {
            'successful_seeds_by_position': defaultdict(list),
            'position_success_rates': {},
            'seed_value_patterns': defaultdict(list),
            'temporal_patterns': [],
            'sequence_analysis': []
        }
        
        for _, row in self.data.iterrows():
            try:
                if pd.isna(row['随机数序列对应的时间戳']) or pd.isna(row['开出的房间']):
                    continue
                
                period = row['期号']
                draw_timestamp = int(row['开奖时间戳'])
                actual_result = int(row['开出的房间'])
                sequence_timestamps = self.parse_timestamp_list(row['随机数序列对应的时间戳'])
                
                if not sequence_timestamps:
                    continue
                
                # 分析每个位置的种子
                successful_positions = []
                for i, seed in enumerate(sequence_timestamps):
                    predicted = self.unity_random(seed)
                    if predicted == actual_result:
                        analysis['successful_seeds_by_position'][i].append({
                            'period': period,
                            'seed': seed,
                            'position': i,
                            'draw_timestamp': draw_timestamp
                        })
                        successful_positions.append(i)
                
                # 记录序列分析
                analysis['sequence_analysis'].append({
                    'period': period,
                    'draw_timestamp': draw_timestamp,
                    'actual_result': actual_result,
                    'sequence_length': len(sequence_timestamps),
                    'successful_positions': successful_positions,
                    'success_count': len(successful_positions)
                })
                
            except Exception as e:
                print(f"⚠️ 处理期号 {row.get('期号', 'unknown')} 失败: {e}")
                continue
        
        # 计算位置成功率
        total_records = len(analysis['sequence_analysis'])
        for position, seeds in analysis['successful_seeds_by_position'].items():
            success_rate = len(seeds) / total_records if total_records > 0 else 0
            analysis['position_success_rates'][position] = {
                'success_count': len(seeds),
                'total_records': total_records,
                'success_rate': success_rate
            }
        
        return analysis
    
    def find_high_success_positions(self, sequence_analysis: Dict, min_success_rate: float = 0.8) -> List[int]:
        """找出高成功率的位置"""
        high_success_positions = []
        
        for position, stats in sequence_analysis['position_success_rates'].items():
            if stats['success_rate'] >= min_success_rate:
                high_success_positions.append({
                    'position': position,
                    'success_rate': stats['success_rate'],
                    'success_count': stats['success_count']
                })
        
        # 按成功率排序
        high_success_positions.sort(key=lambda x: x['success_rate'], reverse=True)
        return high_success_positions
    
    def analyze_seed_relationships(self, sequence_analysis: Dict) -> Dict:
        """分析种子关系"""
        print("\n🔬 分析种子关系模式...")
        
        relationships = {
            'draw_timestamp_to_seed': [],
            'seed_offset_patterns': Counter(),
            'position_clusters': defaultdict(list)
        }
        
        # 分析开奖时间戳与成功种子的关系
        for position, seeds in sequence_analysis['successful_seeds_by_position'].items():
            for seed_info in seeds:
                seed = seed_info['seed']
                draw_ts = seed_info['draw_timestamp']
                offset = seed - draw_ts
                
                relationships['draw_timestamp_to_seed'].append({
                    'position': position,
                    'draw_timestamp': draw_ts,
                    'seed': seed,
                    'offset': offset
                })
                
                relationships['seed_offset_patterns'][offset] += 1
                relationships['position_clusters'][position].append(offset)
        
        return relationships
    
    def find_true_pattern(self, sequence_analysis: Dict, relationships: Dict) -> Dict:
        """寻找真实模式"""
        print("\n💡 寻找真实的随机数生成模式...")
        
        pattern_analysis = {
            'high_success_positions': [],
            'consistent_offsets': [],
            'pattern_hypothesis': None,
            'confidence_score': 0.0
        }
        
        # 找出高成功率位置
        high_positions = self.find_high_success_positions(sequence_analysis, 0.5)
        pattern_analysis['high_success_positions'] = high_positions
        
        # 分析一致的偏移模式
        offset_patterns = relationships['seed_offset_patterns']
        if offset_patterns:
            # 找出最常见的偏移
            most_common_offsets = offset_patterns.most_common(10)
            pattern_analysis['consistent_offsets'] = most_common_offsets
            
            # 检查是否有明显的模式
            total_occurrences = sum(offset_patterns.values())
            if most_common_offsets:
                top_offset, top_count = most_common_offsets[0]
                confidence = top_count / total_occurrences
                
                if confidence > 0.3:  # 如果最常见偏移占比超过30%
                    pattern_analysis['pattern_hypothesis'] = {
                        'type': 'offset_based',
                        'formula': f'开奖时间戳 + {top_offset}',
                        'confidence': confidence,
                        'occurrences': top_count,
                        'total': total_occurrences
                    }
                    pattern_analysis['confidence_score'] = confidence
        
        # 分析位置模式
        if high_positions:
            # 检查是否有特定位置总是成功
            perfect_positions = [p for p in high_positions if p['success_rate'] >= 0.9]
            if perfect_positions:
                pattern_analysis['pattern_hypothesis'] = {
                    'type': 'position_based',
                    'positions': [p['position'] for p in perfect_positions],
                    'confidence': min(p['success_rate'] for p in perfect_positions),
                    'description': f"位置 {[p['position'] for p in perfect_positions]} 几乎总是正确"
                }
                pattern_analysis['confidence_score'] = min(p['success_rate'] for p in perfect_positions)
        
        return pattern_analysis
    
    def generate_comprehensive_report(self, sequence_analysis: Dict, relationships: Dict, pattern_analysis: Dict) -> str:
        """生成综合报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("🔬 深度模式分析报告")
        lines.append("=" * 80)
        
        # 基本统计
        total_records = len(sequence_analysis['sequence_analysis'])
        total_positions = len(sequence_analysis['position_success_rates'])
        
        lines.append(f"📊 分析概览:")
        lines.append(f"  • 分析记录数: {total_records}")
        lines.append(f"  • 序列位置数: {total_positions}")
        lines.append(f"  • 成功种子总数: {sum(len(seeds) for seeds in sequence_analysis['successful_seeds_by_position'].values())}")
        lines.append("")
        
        # 高成功率位置
        high_positions = pattern_analysis['high_success_positions']
        if high_positions:
            lines.append("🎯 高成功率位置 (成功率 > 50%):")
            for pos in high_positions[:10]:  # 显示前10个
                lines.append(f"  • 位置 {pos['position']}: {pos['success_rate']:.1%} ({pos['success_count']}/{total_records})")
            lines.append("")
        else:
            lines.append("🎯 未发现明显的高成功率位置")
            lines.append("")
        
        # 偏移模式分析
        consistent_offsets = pattern_analysis['consistent_offsets']
        if consistent_offsets:
            lines.append("📈 种子偏移模式 (前10个):")
            for offset, count in consistent_offsets:
                percentage = count / sum(c for _, c in consistent_offsets) * 100
                lines.append(f"  • 偏移 {offset}: {count} 次 ({percentage:.1f}%)")
            lines.append("")
        
        # 模式假设
        hypothesis = pattern_analysis['pattern_hypothesis']
        if hypothesis:
            lines.append("💡 发现的模式:")
            if hypothesis['type'] == 'offset_based':
                lines.append(f"  • 类型: 基于偏移的模式")
                lines.append(f"  • 公式: {hypothesis['formula']}")
                lines.append(f"  • 置信度: {hypothesis['confidence']:.1%}")
                lines.append(f"  • 出现次数: {hypothesis['occurrences']}/{hypothesis['total']}")
            elif hypothesis['type'] == 'position_based':
                lines.append(f"  • 类型: 基于位置的模式")
                lines.append(f"  • 关键位置: {hypothesis['positions']}")
                lines.append(f"  • 置信度: {hypothesis['confidence']:.1%}")
                lines.append(f"  • 描述: {hypothesis['description']}")
            lines.append("")
        else:
            lines.append("💡 未发现明确的模式")
            lines.append("")
        
        # 结论
        lines.append("🎯 分析结论:")
        confidence = pattern_analysis['confidence_score']
        if confidence > 0.8:
            lines.append("  ✅ 发现了高置信度的模式，可用于预测")
        elif confidence > 0.5:
            lines.append("  ⚠️ 发现了中等置信度的模式，需要进一步验证")
        elif confidence > 0.3:
            lines.append("  🔍 发现了弱模式，建议谨慎使用")
        else:
            lines.append("  ❌ 未发现可靠的预测模式")
            lines.append("  💭 可能的原因:")
            lines.append("     - 游戏使用了更复杂的随机数生成机制")
            lines.append("     - 存在额外的加密或变换步骤")
            lines.append("     - 真实种子来源不是时间戳")
        
        lines.append("=" * 80)
        return "\n".join(lines)
    
    def run_deep_analysis(self) -> Dict:
        """运行深度分析"""
        print("🚀 开始深度模式分析...")
        
        # 分析序列模式
        sequence_analysis = self.analyze_sequence_patterns()
        
        # 分析种子关系
        relationships = self.analyze_seed_relationships(sequence_analysis)
        
        # 寻找真实模式
        pattern_analysis = self.find_true_pattern(sequence_analysis, relationships)
        
        # 生成报告
        report = self.generate_comprehensive_report(sequence_analysis, relationships, pattern_analysis)
        print(report)
        
        return {
            'sequence_analysis': sequence_analysis,
            'relationships': relationships,
            'pattern_analysis': pattern_analysis,
            'report': report
        }

def main():
    """主函数"""
    analyzer = DeepPatternAnalyzer("real_time_data_20250817.csv")
    results = analyzer.run_deep_analysis()
    
    # 保存结果
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"deep_pattern_analysis_{timestamp}.json"
    
    # 保存可序列化的结果
    serializable_results = {
        'report': results['report'],
        'pattern_analysis': results['pattern_analysis'],
        'high_success_positions': results['pattern_analysis']['high_success_positions'][:20],
        'consistent_offsets': results['pattern_analysis']['consistent_offsets'][:20]
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 深度分析结果已保存到: {filename}")

if __name__ == "__main__":
    main()
