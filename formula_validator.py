#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实种子公式验证工具
使用实际开奖数据验证发现的种子公式
"""

import pandas as pd
import random
from typing import List, Dict, Tuple
from collections import Counter

class FormulaValidator:
    """公式验证器"""
    
    def __init__(self, data_file: str = "real_time_data_20250817.csv"):
        self.data_file = data_file
        self.data = None
        
        # 发现的种子公式（按置信度排序）
        self.formulas = [
            {'offset': -616, 'name': '主要公式', 'expected_confidence': 0.86},
            {'offset': -934, 'name': '备选公式1', 'expected_confidence': 0.82},
            {'offset': 867, 'name': '备选公式2', 'expected_confidence': 0.79},
            {'offset': 895, 'name': '备选公式3', 'expected_confidence': 0.77},
            {'offset': -358, 'name': '备选公式4', 'expected_confidence': 0.77},
        ]
        
        self.load_data()
    
    def load_data(self):
        """加载开奖数据"""
        print(f"📂 加载开奖数据: {self.data_file}")
        self.data = pd.read_csv(self.data_file, encoding='utf-8')
        print(f"✅ 加载完成，共 {len(self.data)} 条记录")
        
        # 显示数据样本
        print("\n📋 数据样本:")
        print("期号 | 开奖时间戳 | 开出的房间")
        print("-" * 30)
        for _, row in self.data.head(5).iterrows():
            if not pd.isna(row['开奖时间戳']) and not pd.isna(row['开出的房间']):
                print(f"{row['期号']} | {int(row['开奖时间戳'])} | {int(row['开出的房间'])}")
    
    def unity_random(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def validate_single_formula(self, offset: int) -> Dict:
        """
        验证单个公式
        
        Args:
            offset: 时间戳偏移量
            
        Returns:
            验证结果
        """
        results = {
            'offset': offset,
            'total_tests': 0,
            'correct_predictions': 0,
            'accuracy': 0.0,
            'detailed_results': [],
            'error_cases': []
        }
        
        for _, row in self.data.iterrows():
            try:
                # 跳过无效数据
                if pd.isna(row['开奖时间戳']) or pd.isna(row['开出的房间']):
                    continue
                
                # 获取数据
                period = row['期号']
                draw_timestamp = int(row['开奖时间戳'])
                actual_result = int(row['开出的房间'])
                
                # 计算真实种子
                true_seed = draw_timestamp + offset
                
                # 生成预测
                predicted_result = self.unity_random(true_seed)
                
                # 检查是否正确
                is_correct = predicted_result == actual_result
                
                results['total_tests'] += 1
                if is_correct:
                    results['correct_predictions'] += 1
                
                # 记录详细结果
                result_detail = {
                    'period': period,
                    'draw_timestamp': draw_timestamp,
                    'true_seed': true_seed,
                    'predicted': predicted_result,
                    'actual': actual_result,
                    'correct': is_correct
                }
                
                results['detailed_results'].append(result_detail)
                
                if not is_correct:
                    results['error_cases'].append(result_detail)
                
            except Exception as e:
                print(f"⚠️ 处理期号 {row.get('期号', 'unknown')} 时出错: {e}")
                continue
        
        # 计算准确率
        if results['total_tests'] > 0:
            results['accuracy'] = results['correct_predictions'] / results['total_tests']
        
        return results
    
    def validate_all_formulas(self) -> Dict:
        """验证所有公式"""
        print("\n🔬 开始验证所有发现的公式...")
        
        all_results = {}
        
        for formula in self.formulas:
            offset = formula['offset']
            name = formula['name']
            
            print(f"\n📊 验证 {name} (偏移量: {offset})...")
            
            result = self.validate_single_formula(offset)
            result['name'] = name
            result['expected_confidence'] = formula['expected_confidence']
            
            all_results[offset] = result
            
            print(f"   测试数量: {result['total_tests']}")
            print(f"   正确预测: {result['correct_predictions']}")
            print(f"   准确率: {result['accuracy']:.2%}")
        
        return all_results
    
    def analyze_prediction_patterns(self, validation_results: Dict) -> Dict:
        """分析预测模式"""
        print("\n🔍 分析预测模式...")
        
        analysis = {
            'best_formula': None,
            'accuracy_ranking': [],
            'prediction_distribution': {},
            'error_analysis': {},
            'consistency_check': {}
        }
        
        # 按准确率排序
        sorted_results = sorted(validation_results.items(), 
                              key=lambda x: x[1]['accuracy'], reverse=True)
        
        analysis['accuracy_ranking'] = [
            {
                'offset': offset,
                'name': result['name'],
                'accuracy': result['accuracy'],
                'correct': result['correct_predictions'],
                'total': result['total_tests']
            }
            for offset, result in sorted_results
        ]
        
        # 最佳公式
        if sorted_results:
            best_offset, best_result = sorted_results[0]
            analysis['best_formula'] = {
                'offset': best_offset,
                'name': best_result['name'],
                'accuracy': best_result['accuracy'],
                'expected_accuracy': best_result['expected_confidence']
            }
        
        # 预测分布分析
        for offset, result in validation_results.items():
            predicted_numbers = [r['predicted'] for r in result['detailed_results']]
            actual_numbers = [r['actual'] for r in result['detailed_results']]
            
            analysis['prediction_distribution'][offset] = {
                'predicted_counts': dict(Counter(predicted_numbers)),
                'actual_counts': dict(Counter(actual_numbers))
            }
        
        # 错误分析
        for offset, result in validation_results.items():
            if result['error_cases']:
                error_patterns = Counter(
                    (r['predicted'], r['actual']) for r in result['error_cases']
                )
                analysis['error_analysis'][offset] = {
                    'error_count': len(result['error_cases']),
                    'common_errors': error_patterns.most_common(5)
                }
        
        return analysis
    
    def generate_validation_report(self, validation_results: Dict, analysis: Dict) -> str:
        """生成验证报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("🔬 真实种子公式验证报告")
        lines.append("=" * 80)
        
        # 数据概览
        if validation_results:
            sample_result = next(iter(validation_results.values()))
            lines.append(f"📊 验证数据概览:")
            lines.append(f"  • 测试记录数: {sample_result['total_tests']}")
            lines.append(f"  • 验证公式数: {len(validation_results)}")
            lines.append("")
        
        # 准确率排名
        lines.append("🏆 公式准确率排名:")
        for i, formula in enumerate(analysis['accuracy_ranking'], 1):
            lines.append(f"  {i}. {formula['name']} (偏移: {formula['offset']})")
            lines.append(f"     准确率: {formula['accuracy']:.2%} ({formula['correct']}/{formula['total']})")
            lines.append("")
        
        # 最佳公式分析
        if analysis['best_formula']:
            best = analysis['best_formula']
            lines.append("🎯 最佳公式分析:")
            lines.append(f"  • 公式: 开奖时间戳 + {best['offset']}")
            lines.append(f"  • 实际准确率: {best['accuracy']:.2%}")
            lines.append(f"  • 预期准确率: {best['expected_accuracy']:.2%}")
            
            accuracy_diff = best['accuracy'] - best['expected_accuracy']
            if accuracy_diff > 0:
                lines.append(f"  • 性能: 超出预期 {accuracy_diff:.2%} ✅")
            else:
                lines.append(f"  • 性能: 低于预期 {abs(accuracy_diff):.2%} ⚠️")
            lines.append("")
        
        # 预测分布分析
        lines.append("📈 预测分布分析:")
        for offset, result in validation_results.items():
            if offset in analysis['prediction_distribution']:
                dist = analysis['prediction_distribution'][offset]
                lines.append(f"  公式 {offset}:")
                
                pred_counts = dist['predicted_counts']
                actual_counts = dist['actual_counts']
                
                lines.append("    预测分布: " + 
                           ", ".join(f"{num}:{count}" for num, count in sorted(pred_counts.items())))
                lines.append("    实际分布: " + 
                           ", ".join(f"{num}:{count}" for num, count in sorted(actual_counts.items())))
                lines.append("")
        
        # 错误分析
        if analysis['error_analysis']:
            lines.append("❌ 错误模式分析:")
            for offset, error_info in analysis['error_analysis'].items():
                if error_info['common_errors']:
                    lines.append(f"  公式 {offset} 常见错误:")
                    for (pred, actual), count in error_info['common_errors']:
                        lines.append(f"    预测{pred}→实际{actual}: {count}次")
                    lines.append("")
        
        # 结论和建议
        lines.append("💡 结论和建议:")
        if analysis['best_formula']:
            best = analysis['best_formula']
            if best['accuracy'] > 0.8:
                lines.append(f"  ✅ 公式验证成功！推荐使用: 开奖时间戳 + {best['offset']}")
                lines.append(f"  📊 该公式准确率达到 {best['accuracy']:.2%}，可用于实际预测")
            elif best['accuracy'] > 0.5:
                lines.append(f"  ⚠️ 公式有一定效果，但准确率偏低: {best['accuracy']:.2%}")
                lines.append(f"  🔍 建议进一步分析或寻找其他模式")
            else:
                lines.append(f"  ❌ 公式验证失败，准确率过低: {best['accuracy']:.2%}")
                lines.append(f"  🔄 建议重新分析数据或检查算法")
        
        lines.append("=" * 80)
        return "\n".join(lines)
    
    def run_complete_validation(self) -> Dict:
        """运行完整验证"""
        print("🚀 开始完整的公式验证...")
        
        # 验证所有公式
        validation_results = self.validate_all_formulas()
        
        # 分析结果
        analysis = self.analyze_prediction_patterns(validation_results)
        
        # 生成报告
        report = self.generate_validation_report(validation_results, analysis)
        print(report)
        
        return {
            'validation_results': validation_results,
            'analysis': analysis,
            'report': report
        }
    
    def export_detailed_results(self, validation_results: Dict, filename: str = None):
        """导出详细验证结果"""
        if filename is None:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"formula_validation_details_{timestamp}.csv"
        
        # 准备导出数据
        export_data = []
        
        for offset, result in validation_results.items():
            for detail in result['detailed_results']:
                export_data.append({
                    'formula_offset': offset,
                    'period': detail['period'],
                    'draw_timestamp': detail['draw_timestamp'],
                    'true_seed': detail['true_seed'],
                    'predicted_result': detail['predicted'],
                    'actual_result': detail['actual'],
                    'prediction_correct': detail['correct']
                })
        
        # 保存到CSV
        df = pd.DataFrame(export_data)
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"📁 详细验证结果已导出到: {filename}")
        
        return filename

def main():
    """主函数"""
    validator = FormulaValidator("real_time_data_20250817.csv")
    
    # 运行完整验证
    results = validator.run_complete_validation()
    
    # 导出详细结果
    validator.export_detailed_results(results['validation_results'])
    
    # 保存验证报告
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_filename = f"formula_validation_report_{timestamp}.json"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        json.dump({
            'report': results['report'],
            'analysis': results['analysis'],
            'summary': {
                'best_formula': results['analysis']['best_formula'],
                'accuracy_ranking': results['analysis']['accuracy_ranking']
            }
        }, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 验证报告已保存到: {report_filename}")

if __name__ == "__main__":
    main()
