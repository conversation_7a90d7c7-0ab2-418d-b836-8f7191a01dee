#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测号码准确率统计系统 - 配置文件

本文件包含系统的所有可配置参数，用户可以根据需要修改这些参数来自定义分析行为。
"""

# =============================================================================
# 数据文件配置
# =============================================================================

# 数据文件处理模式
# 'single': 处理单个文件
# 'multiple': 处理多个文件
# 'pattern': 按文件名模式处理
DATA_MODE = 'multiple'

# 单文件模式：输入数据文件名
DATA_FILE = 'real_time_data_20250817.csv'

# 多文件模式：数据文件目录
DATA_DIRECTORY = '.'  # 当前目录

# 文件名模式（支持通配符）
DATA_FILE_PATTERN = '*.csv'  # 所有CSV文件
# DATA_FILE_PATTERN = 'real_time_data_*.csv'  # 特定模式的CSV文件

# 排除的文件名列表（这些文件不会被处理）
EXCLUDE_FILES = [
    'sorted_index_accuracy_report.csv',
    'region_accuracy_report.csv',
    'output.csv',
    'result.csv'
]

# CSV文件编码
FILE_ENCODING = 'utf-8'

# CSV字段大小限制 (字节)
CSV_FIELD_SIZE_LIMIT = 10000000  # 10MB

# =============================================================================
# 索引位置统计配置
# =============================================================================

# 显示排名前N个索引位置
TOP_INDEX_DISPLAY_COUNT = 50

# 保存报告中的详细记录数量
INDEX_DETAIL_RECORD_COUNT = 100

# 索引位置统计报告文件名
INDEX_REPORT_FILENAME = 'sorted_index_accuracy_report.txt'

# =============================================================================
# 区域统计配置
# =============================================================================

# 要测试的区域大小列表
REGION_SIZES_TO_TEST = [4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40]  # 完整的滑动窗口模式测试

# 推荐的区域大小 (基于标准差最大原则)
RECOMMENDED_REGION_SIZE = 5

# 显示排名前N个区域
TOP_REGION_DISPLAY_COUNT = 50

# 保存报告中的详细记录数量
REGION_DETAIL_RECORD_COUNT = 20

# 区域统计报告文件名模板
REGION_REPORT_FILENAME_TEMPLATE = 'region_size_{size}_accuracy_report.txt'

# =============================================================================
# 准确率区间配置
# =============================================================================

# 索引位置准确率区间定义 (最小值, 最大值, 标签)
INDEX_ACCURACY_RANGES = [
    (20, float('inf'), "20%以上"),
    (15, 20, "15%-20%"),
    (10, 15, "10%-15%"),
    (5, 10, "5%-10%"),
    (0, 5, "0%-5%")
]

# 区域准确率区间定义 (最小值, 最大值, 标签)
REGION_ACCURACY_RANGES = [
    (80, float('inf'), "80%以上"),
    (60, 80, "60%-80%"),
    (40, 60, "40%-60%"),
    (20, 40, "20%-40%"),
    (10, 20, "10%-20%"),
    (0, 10, "10%以下")
]

# =============================================================================
# 性能优化配置
# =============================================================================

# 是否启用进度显示
SHOW_PROGRESS = True

# 进度显示间隔 (每处理N行显示一次)
PROGRESS_INTERVAL = 50

# 是否启用详细日志
ENABLE_VERBOSE_LOGGING = False

# =============================================================================
# 输出格式配置
# =============================================================================

# 准确率显示精度 (小数点后位数)
ACCURACY_PRECISION = 2

# 表格列宽配置
TABLE_COLUMN_WIDTHS = {
    'rank': 3,      # 排名列宽度
    'index': 4,     # 索引列宽度
    'region': 3,    # 区域列宽度
    'range': 12,    # 范围列宽度
    'count': 3,     # 计数列宽度
    'accuracy': 6   # 准确率列宽度
}

# =============================================================================
# 数据验证配置
# =============================================================================

# 开出号码的有效范围
VALID_NUMBER_RANGE = (1, 8)

# 最小有效记录数量 (少于此数量会发出警告)
MIN_VALID_RECORDS = 50

# 预测序列的期望长度
EXPECTED_SEQUENCE_LENGTH = 9000

# =============================================================================
# 高级配置
# =============================================================================

# 是否保存中间计算结果
SAVE_INTERMEDIATE_RESULTS = False

# 中间结果文件名前缀
INTERMEDIATE_RESULTS_PREFIX = 'intermediate_'

# 是否启用并行处理 (实验性功能)
ENABLE_PARALLEL_PROCESSING = False

# 并行处理的进程数 (0表示自动检测)
PARALLEL_PROCESS_COUNT = 0

# =============================================================================
# 自定义函数
# =============================================================================

def get_data_files():
    """
    根据配置获取要处理的数据文件列表

    Returns:
        list: 数据文件路径列表
    """
    import os
    import glob

    if DATA_MODE == 'single':
        # 单文件模式
        if os.path.exists(DATA_FILE):
            return [DATA_FILE]
        else:
            print(f"⚠️ 警告: 文件 {DATA_FILE} 不存在")
            return []

    elif DATA_MODE == 'multiple' or DATA_MODE == 'pattern':
        # 多文件模式或模式匹配
        pattern_path = os.path.join(DATA_DIRECTORY, DATA_FILE_PATTERN)
        all_files = glob.glob(pattern_path)

        # 过滤掉排除的文件
        data_files = []
        for file_path in all_files:
            filename = os.path.basename(file_path)
            if filename not in EXCLUDE_FILES:
                data_files.append(file_path)

        # 按文件名排序
        data_files.sort()

        print(f"📁 发现 {len(data_files)} 个数据文件:")
        for i, file_path in enumerate(data_files, 1):
            file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
            print(f"  {i}. {os.path.basename(file_path)} ({file_size:.2f} MB)")

        return data_files

    else:
        print(f"❌ 错误: 不支持的数据模式 '{DATA_MODE}'")
        return []

def get_region_report_filename(size):
    """
    根据区域大小生成报告文件名

    Args:
        size (int): 区域大小

    Returns:
        str: 报告文件名
    """
    return REGION_REPORT_FILENAME_TEMPLATE.format(size=size)

def is_valid_number(number):
    """
    检查号码是否在有效范围内
    
    Args:
        number (int): 要检查的号码
        
    Returns:
        bool: 是否有效
    """
    return VALID_NUMBER_RANGE[0] <= number <= VALID_NUMBER_RANGE[1]

def format_accuracy(accuracy):
    """
    格式化准确率显示
    
    Args:
        accuracy (float): 准确率值
        
    Returns:
        str: 格式化后的准确率字符串
    """
    return f"{accuracy:.{ACCURACY_PRECISION}f}%"

def get_accuracy_range_label(accuracy):
    """
    根据准确率获取对应的区间标签
    
    Args:
        accuracy (float): 准确率值
        
    Returns:
        str: 区间标签
    """
    for min_acc, max_acc, label in INDEX_ACCURACY_RANGES:
        if min_acc <= accuracy < max_acc:
            return label
    return "未知区间"

# =============================================================================
# 配置验证
# =============================================================================

def validate_config():
    """
    验证配置参数的有效性
    
    Raises:
        ValueError: 当配置参数无效时
    """
    # 验证区域大小
    if not all(size > 0 for size in REGION_SIZES_TO_TEST):
        raise ValueError("区域大小必须大于0")
    
    # 验证显示数量
    if TOP_INDEX_DISPLAY_COUNT <= 0 or TOP_REGION_DISPLAY_COUNT <= 0:
        raise ValueError("显示数量必须大于0")
    
    # 验证号码范围
    if VALID_NUMBER_RANGE[0] >= VALID_NUMBER_RANGE[1]:
        raise ValueError("号码范围配置错误")
    
    # 验证精度
    if ACCURACY_PRECISION < 0:
        raise ValueError("准确率精度不能为负数")
    
    print("✅ 配置验证通过")

# =============================================================================
# 使用示例
# =============================================================================

if __name__ == "__main__":
    # 验证配置
    validate_config()
    
    # 显示当前配置
    print("=== 当前配置 ===")
    print(f"数据文件: {DATA_FILE}")
    print(f"推荐区域大小: {RECOMMENDED_REGION_SIZE}")
    print(f"测试区域大小: {REGION_SIZES_TO_TEST}")
    print(f"有效号码范围: {VALID_NUMBER_RANGE[0]}-{VALID_NUMBER_RANGE[1]}")
    print(f"准确率精度: {ACCURACY_PRECISION}位小数")
    
    # 示例：格式化准确率
    print(f"\n示例准确率格式化: {format_accuracy(23.456789)}")
    
    # 示例：获取区间标签
    print(f"准确率18.5%属于: {get_accuracy_range_label(18.5)}")
    
    # 示例：生成报告文件名
    print(f"区域大小5的报告文件: {get_region_report_filename(5)}")
