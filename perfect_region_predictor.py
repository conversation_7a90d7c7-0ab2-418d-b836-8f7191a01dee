#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完美区域预测器
基于100%准确率的滑动窗口进行高精度预测
"""

import random
import time
from datetime import datetime
from typing import List, Dict, Tuple

class PerfectRegionPredictor:
    """完美区域预测器"""
    
    def __init__(self):
        # 100%准确率的滑动窗口区域（基于region_size_38_accuracy_report.txt）
        self.perfect_regions = [
            {'id': 2258, 'start': 2258, 'end': 2295, 'accuracy': 100.00, 'coverage': 96.01},
            {'id': 2259, 'start': 2259, 'end': 2296, 'accuracy': 100.00, 'coverage': 95.93},
            {'id': 4344, 'start': 4344, 'end': 4381, 'accuracy': 100.00, 'coverage': 96.40},
            {'id': 4345, 'start': 4345, 'end': 4382, 'accuracy': 100.00, 'coverage': 96.48},
            {'id': 4346, 'start': 4346, 'end': 4383, 'accuracy': 100.00, 'coverage': 96.17},
        ]
        
        self.game_numbers = list(range(1, 9))  # 1-8号码
    
    def unity_random_next(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def predict_with_perfect_regions(self, timestamp_ms: int) -> Dict:
        """
        使用100%准确率区域进行预测
        
        Args:
            timestamp_ms: 毫秒级时间戳
            
        Returns:
            预测结果字典
        """
        region_results = []
        all_predictions = []
        
        # 对每个100%准确率区域进行预测
        for region in self.perfect_regions:
            region_predictions = []
            
            # 遍历区域内的每个偏移
            for offset in range(region['start'], region['end'] + 1):
                seed = timestamp_ms + offset
                predicted_number = self.unity_random_next(seed)
                region_predictions.append(predicted_number)
                all_predictions.append(predicted_number)
            
            # 统计该区域的号码分布
            number_counts = {}
            for num in region_predictions:
                number_counts[num] = number_counts.get(num, 0) + 1
            
            # 找出该区域最可能避开的号码
            most_frequent = max(number_counts.items(), key=lambda x: x[1])
            
            region_results.append({
                'region_id': region['id'],
                'region_range': f"{region['start']}-{region['end']}",
                'accuracy': region['accuracy'],
                'coverage': region['coverage'],
                'predictions': region_predictions,
                'number_distribution': number_counts,
                'most_avoid': most_frequent[0],
                'avoid_frequency': most_frequent[1],
                'avoid_percentage': (most_frequent[1] / len(region_predictions)) * 100
            })
        
        # 综合分析所有区域的结果
        overall_counts = {}
        for num in all_predictions:
            overall_counts[num] = overall_counts.get(num, 0) + 1
        
        # 找出所有区域共同建议避开的号码
        region_avoid_votes = {}
        for result in region_results:
            avoid_num = result['most_avoid']
            region_avoid_votes[avoid_num] = region_avoid_votes.get(avoid_num, 0) + 1
        
        # 按投票数排序
        sorted_avoid = sorted(region_avoid_votes.items(), key=lambda x: x[1], reverse=True)
        
        # 确定最终避开建议
        consensus_avoid = []
        high_risk = []
        
        for num, votes in sorted_avoid:
            vote_percentage = (votes / len(self.perfect_regions)) * 100
            if vote_percentage >= 60:  # 60%以上区域建议避开
                consensus_avoid.append(num)
            elif vote_percentage >= 40:  # 40-60%为高风险
                high_risk.append(num)
        
        # 计算安全号码
        all_risky = set(consensus_avoid + high_risk)
        safe_numbers = [num for num in self.game_numbers if num not in all_risky]
        
        return {
            'timestamp': timestamp_ms,
            'datetime': datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S'),
            'region_results': region_results,
            'overall_distribution': overall_counts,
            'consensus_avoid': sorted(consensus_avoid),
            'high_risk': sorted(high_risk),
            'safe_numbers': sorted(safe_numbers),
            'confidence': 100.0,  # 基于100%准确率区域
            'total_predictions': len(all_predictions),
            'regions_used': len(self.perfect_regions)
        }
    
    def batch_predict_timeline(self, base_timestamp: int, range_minutes: int = 5) -> List[Dict]:
        """
        批量预测时间线
        
        Args:
            base_timestamp: 基础时间戳（毫秒）
            range_minutes: 时间范围（分钟）
            
        Returns:
            时间线预测结果列表
        """
        results = []
        range_ms = range_minutes * 60 * 1000  # 转换为毫秒
        
        # 每30秒预测一次
        for offset_ms in range(-range_ms, range_ms + 1, 30000):
            target_timestamp = base_timestamp + offset_ms
            prediction = self.predict_with_perfect_regions(target_timestamp)
            results.append(prediction)
        
        return results
    
    def analyze_timeline_consensus(self, timeline_results: List[Dict]) -> Dict:
        """
        分析时间线预测的共识
        
        Args:
            timeline_results: 时间线预测结果
            
        Returns:
            共识分析结果
        """
        avoid_consensus = {}
        safe_consensus = {}
        
        for result in timeline_results:
            # 统计避开号码的出现频次
            for num in result['consensus_avoid']:
                avoid_consensus[num] = avoid_consensus.get(num, 0) + 1
            
            # 统计安全号码的出现频次
            for num in result['safe_numbers']:
                safe_consensus[num] = safe_consensus.get(num, 0) + 1
        
        total_predictions = len(timeline_results)
        
        # 计算强共识（80%以上时间点都建议的）
        strong_avoid = [num for num, count in avoid_consensus.items() 
                       if count >= total_predictions * 0.8]
        strong_safe = [num for num, count in safe_consensus.items() 
                      if count >= total_predictions * 0.8]
        
        return {
            'total_timeline_points': total_predictions,
            'avoid_frequency': avoid_consensus,
            'safe_frequency': safe_consensus,
            'strong_consensus_avoid': sorted(strong_avoid),
            'strong_consensus_safe': sorted(strong_safe),
            'avoid_percentages': {num: (count/total_predictions)*100 
                                for num, count in avoid_consensus.items()},
            'safe_percentages': {num: (count/total_predictions)*100 
                               for num, count in safe_consensus.items()}
        }
    
    def format_prediction_report(self, prediction: Dict) -> str:
        """格式化预测报告"""
        report = []
        report.append("🎯 完美区域预测报告")
        report.append("=" * 60)
        
        report.append(f"⏰ 预测时间: {prediction['datetime']}")
        report.append(f"📊 置信度: {prediction['confidence']:.1f}% (基于100%准确率区域)")
        report.append(f"🔢 使用区域数: {prediction['regions_used']}")
        report.append(f"📈 总预测数: {prediction['total_predictions']}")
        report.append("")
        
        report.append("📍 各区域详细结果:")
        for i, result in enumerate(prediction['region_results'], 1):
            report.append(f"  {i}. 区域 {result['region_range']} (ID: {result['region_id']})")
            report.append(f"     准确率: {result['accuracy']:.2f}%, 覆盖率: {result['coverage']:.2f}%")
            report.append(f"     最建议避开: 号码{result['most_avoid']} (出现{result['avoid_frequency']}次, {result['avoid_percentage']:.1f}%)")
        report.append("")
        
        report.append("🚫 综合避开建议:")
        if prediction['consensus_avoid']:
            report.append(f"   强烈建议避开: {prediction['consensus_avoid']}")
        if prediction['high_risk']:
            report.append(f"   高风险号码: {prediction['high_risk']}")
        report.append("")
        
        report.append("✅ 安全选择建议:")
        if prediction['safe_numbers']:
            report.append(f"   推荐选择: {prediction['safe_numbers']}")
        else:
            report.append("   ⚠️ 所有号码都有一定风险，建议谨慎投注")
        report.append("")
        
        report.append("📊 整体号码分布:")
        for num in sorted(prediction['overall_distribution'].keys()):
            count = prediction['overall_distribution'][num]
            percentage = (count / prediction['total_predictions']) * 100
            report.append(f"   号码{num}: {count}次 ({percentage:.1f}%)")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def interactive_mode(self):
        """交互式预测模式"""
        print("🎯 完美区域预测器 - 基于100%准确率区域")
        print("=" * 60)
        print("功能说明:")
        print("  1 - 当前时间预测")
        print("  2 - 自定义时间戳预测")
        print("  3 - 时间线批量预测")
        print("  4 - 查看完美区域信息")
        print("  q - 退出")
        print("=" * 60)
        
        while True:
            try:
                choice = input("\n请选择功能 (1-4, q): ").strip().lower()
                
                if choice == 'q':
                    print("👋 感谢使用完美区域预测器！")
                    break
                
                elif choice == '1':
                    current_timestamp = int(time.time() * 1000)
                    prediction = self.predict_with_perfect_regions(current_timestamp)
                    print(self.format_prediction_report(prediction))
                
                elif choice == '2':
                    timestamp_input = input("请输入时间戳（毫秒）: ").strip()
                    try:
                        timestamp = int(timestamp_input)
                        prediction = self.predict_with_perfect_regions(timestamp)
                        print(self.format_prediction_report(prediction))
                    except ValueError:
                        print("❌ 时间戳格式错误")
                
                elif choice == '3':
                    try:
                        base_ts = int(input("请输入基础时间戳（毫秒，回车使用当前时间）: ").strip() or str(int(time.time() * 1000)))
                        range_min = int(input("请输入时间范围（分钟，默认5）: ").strip() or "5")
                        
                        print(f"🔄 正在分析 {range_min*2+1} 个时间点...")
                        timeline_results = self.batch_predict_timeline(base_ts, range_min)
                        consensus = self.analyze_timeline_consensus(timeline_results)
                        
                        print("\n📊 时间线共识分析:")
                        print("=" * 40)
                        print(f"分析时间点数: {consensus['total_timeline_points']}")
                        print(f"强共识避开: {consensus['strong_consensus_avoid']}")
                        print(f"强共识安全: {consensus['strong_consensus_safe']}")
                        print("\n各号码避开频率:")
                        for num, pct in sorted(consensus['avoid_percentages'].items()):
                            print(f"  号码{num}: {pct:.1f}%")
                        
                    except ValueError:
                        print("❌ 输入格式错误")
                
                elif choice == '4':
                    print("\n🎯 100%准确率完美区域信息:")
                    print("=" * 50)
                    for i, region in enumerate(self.perfect_regions, 1):
                        print(f"{i}. 区域ID: {region['id']}")
                        print(f"   范围: {region['start']}-{region['end']} (大小: {region['end']-region['start']+1})")
                        print(f"   准确率: {region['accuracy']:.2f}%")
                        print(f"   覆盖率: {region['coverage']:.2f}%")
                        print()
                
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    predictor = PerfectRegionPredictor()
    predictor.interactive_mode()

if __name__ == "__main__":
    main()
