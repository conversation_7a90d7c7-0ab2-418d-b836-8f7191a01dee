#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实种子发现工具
基于100%准确率区域，逆向分析真实的随机数种子
"""

import pandas as pd
import ast
import random
from typing import List, Dict, Tuple
from collections import defaultdict, Counter

class TrueSeedFinder:
    """真实种子发现器"""
    
    def __init__(self, data_file: str = "real_time_data_20250817.csv"):
        self.data_file = data_file
        self.data = None
        
        # 100%准确率的区域（来自region_size_38_accuracy_report.txt）
        self.perfect_regions = [
            {'id': 2258, 'start': 2258, 'end': 2295},
            {'id': 2259, 'start': 2259, 'end': 2296},
            {'id': 4344, 'start': 4344, 'end': 4381},
            {'id': 4345, 'start': 4345, 'end': 4382},
            {'id': 4346, 'start': 4346, 'end': 4383},
        ]
        
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        print(f"📂 加载数据: {self.data_file}")
        self.data = pd.read_csv(self.data_file, encoding='utf-8')
        print(f"✅ 加载完成，共 {len(self.data)} 条记录")
    
    def parse_timestamp_list(self, timestamp_str: str) -> List[int]:
        """解析时间戳列表"""
        try:
            if pd.isna(timestamp_str):
                return []
            return ast.literal_eval(str(timestamp_str))
        except:
            return []
    
    def unity_random(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def analyze_perfect_region(self, region: Dict) -> Dict:
        """分析单个100%准确率区域"""
        print(f"\n🔍 分析区域 {region['id']} (索引 {region['start']}-{region['end']})")
        
        results = {
            'region_id': region['id'],
            'successful_seeds': [],
            'failed_records': [],
            'seed_patterns': defaultdict(list),
            'statistics': {
                'total_records': 0,
                'valid_records': 0,
                'perfect_avoids': 0,
                'partial_avoids': 0
            }
        }
        
        for _, row in self.data.iterrows():
            results['statistics']['total_records'] += 1
            
            try:
                # 解析数据
                timestamp_list = self.parse_timestamp_list(row['随机数序列对应的时间戳'])
                actual_result = int(row['开出的房间'])
                period = row['期号']
                
                if len(timestamp_list) <= region['end']:
                    continue
                
                results['statistics']['valid_records'] += 1
                
                # 提取区域种子
                region_seeds = timestamp_list[region['start']:region['end']+1]
                
                # 测试每个种子
                avoid_count = 0
                seed_results = []
                
                for i, seed in enumerate(region_seeds):
                    predicted = self.unity_random(seed)
                    is_avoid = predicted != actual_result
                    
                    seed_results.append({
                        'index': region['start'] + i,
                        'seed': seed,
                        'predicted': predicted,
                        'actual': actual_result,
                        'avoid_success': is_avoid
                    })
                    
                    if is_avoid:
                        avoid_count += 1
                        results['successful_seeds'].append({
                            'period': period,
                            'seed': seed,
                            'index': region['start'] + i,
                            'predicted': predicted,
                            'actual': actual_result
                        })
                        
                        # 记录种子模式
                        results['seed_patterns'][seed].append({
                            'period': period,
                            'predicted': predicted,
                            'actual': actual_result
                        })
                
                # 统计避开情况
                if avoid_count == len(region_seeds):
                    results['statistics']['perfect_avoids'] += 1
                elif avoid_count > 0:
                    results['statistics']['partial_avoids'] += 1
                
            except Exception as e:
                results['failed_records'].append({
                    'period': row.get('期号', 'unknown'),
                    'error': str(e)
                })
        
        return results
    
    def find_seed_relationships(self, all_results: List[Dict]) -> Dict:
        """寻找种子之间的关系"""
        print("\n🔬 分析种子关系模式...")
        
        # 收集所有成功的种子
        all_successful_seeds = []
        for result in all_results:
            all_successful_seeds.extend(result['successful_seeds'])
        
        # 按期号分组
        period_groups = defaultdict(list)
        for seed_info in all_successful_seeds:
            period_groups[seed_info['period']].append(seed_info)
        
        # 分析种子模式
        analysis = {
            'period_count': len(period_groups),
            'total_successful_seeds': len(all_successful_seeds),
            'seed_frequency': Counter(),
            'prediction_frequency': Counter(),
            'seed_differences': [],
            'common_patterns': {},
            'period_analysis': {}
        }
        
        # 统计频次
        for seed_info in all_successful_seeds:
            analysis['seed_frequency'][seed_info['seed']] += 1
            analysis['prediction_frequency'][seed_info['predicted']] += 1
        
        # 分析每个期号的种子模式
        for period, seeds in period_groups.items():
            if len(seeds) > 1:
                seeds_sorted = sorted(seeds, key=lambda x: x['seed'])
                differences = []
                for i in range(1, len(seeds_sorted)):
                    diff = seeds_sorted[i]['seed'] - seeds_sorted[i-1]['seed']
                    differences.append(diff)
                    analysis['seed_differences'].append(diff)
                
                analysis['period_analysis'][period] = {
                    'seed_count': len(seeds),
                    'seeds': [s['seed'] for s in seeds_sorted],
                    'predictions': [s['predicted'] for s in seeds_sorted],
                    'differences': differences
                }
        
        # 寻找常见的差值模式
        if analysis['seed_differences']:
            diff_counter = Counter(analysis['seed_differences'])
            analysis['common_patterns'] = {
                'most_common_differences': diff_counter.most_common(10),
                'average_difference': sum(analysis['seed_differences']) / len(analysis['seed_differences']),
                'unique_differences': len(set(analysis['seed_differences']))
            }
        
        return analysis
    
    def generate_true_seed_hypothesis(self, relationship_analysis: Dict) -> Dict:
        """生成真实种子假设"""
        print("\n💡 生成真实种子假设...")
        
        hypothesis = {
            'most_likely_seeds': [],
            'seed_generation_pattern': None,
            'confidence_analysis': {},
            'recommendations': []
        }
        
        # 分析最可能的真实种子
        seed_freq = relationship_analysis['seed_frequency']
        if seed_freq:
            # 按出现频次排序
            most_common_seeds = seed_freq.most_common(20)
            hypothesis['most_likely_seeds'] = most_common_seeds
            
            # 分析种子生成模式
            if relationship_analysis['common_patterns']:
                common_diffs = relationship_analysis['common_patterns']['most_common_differences']
                if common_diffs:
                    most_common_diff = common_diffs[0][0]
                    hypothesis['seed_generation_pattern'] = {
                        'type': 'sequential_with_offset',
                        'common_offset': most_common_diff,
                        'confidence': common_diffs[0][1] / len(relationship_analysis['seed_differences'])
                    }
        
        # 置信度分析
        total_periods = relationship_analysis['period_count']
        successful_periods = len([p for p in relationship_analysis['period_analysis'].values() if p['seed_count'] > 0])
        
        hypothesis['confidence_analysis'] = {
            'coverage_rate': successful_periods / total_periods if total_periods > 0 else 0,
            'average_seeds_per_period': relationship_analysis['total_successful_seeds'] / total_periods if total_periods > 0 else 0,
            'pattern_consistency': len(set(relationship_analysis['seed_differences'])) / len(relationship_analysis['seed_differences']) if relationship_analysis['seed_differences'] else 0
        }
        
        # 生成建议
        if hypothesis['most_likely_seeds']:
            top_seed = hypothesis['most_likely_seeds'][0]
            hypothesis['recommendations'].append(f"最可能的真实种子: {top_seed[0]} (出现 {top_seed[1]} 次)")
        
        if hypothesis['seed_generation_pattern']:
            pattern = hypothesis['seed_generation_pattern']
            hypothesis['recommendations'].append(f"种子生成模式: 偏移量 {pattern['common_offset']} (置信度: {pattern['confidence']:.2%})")
        
        return hypothesis
    
    def run_analysis(self) -> Dict:
        """运行完整分析"""
        print("🚀 开始真实种子发现分析...")
        
        # 分析所有100%准确率区域
        all_results = []
        for region in self.perfect_regions:
            result = self.analyze_perfect_region(region)
            all_results.append(result)
        
        # 分析种子关系
        relationship_analysis = self.find_seed_relationships(all_results)
        
        # 生成假设
        hypothesis = self.generate_true_seed_hypothesis(relationship_analysis)
        
        # 汇总结果
        final_result = {
            'region_results': all_results,
            'relationship_analysis': relationship_analysis,
            'hypothesis': hypothesis,
            'summary': self.generate_summary(all_results, relationship_analysis, hypothesis)
        }
        
        return final_result
    
    def generate_summary(self, all_results: List[Dict], relationship_analysis: Dict, hypothesis: Dict) -> str:
        """生成分析摘要"""
        lines = []
        lines.append("=" * 80)
        lines.append("🔬 真实种子发现分析报告")
        lines.append("=" * 80)
        
        # 区域分析摘要
        lines.append("\n📊 区域分析摘要:")
        for result in all_results:
            stats = result['statistics']
            lines.append(f"  区域 {result['region_id']}:")
            lines.append(f"    • 有效记录: {stats['valid_records']}/{stats['total_records']}")
            lines.append(f"    • 完美避开: {stats['perfect_avoids']}")
            lines.append(f"    • 成功种子: {len(result['successful_seeds'])}")
        
        # 关系分析摘要
        lines.append(f"\n🔍 种子关系分析:")
        lines.append(f"  • 分析期号数: {relationship_analysis['period_count']}")
        lines.append(f"  • 成功种子总数: {relationship_analysis['total_successful_seeds']}")
        
        if relationship_analysis['common_patterns']:
            patterns = relationship_analysis['common_patterns']
            lines.append(f"  • 平均种子差值: {patterns['average_difference']:.2f}")
            lines.append(f"  • 最常见差值: {patterns['most_common_differences'][0] if patterns['most_common_differences'] else 'N/A'}")
        
        # 假设摘要
        lines.append(f"\n💡 真实种子假设:")
        if hypothesis['most_likely_seeds']:
            top_seeds = hypothesis['most_likely_seeds'][:5]
            lines.append("  最可能的真实种子:")
            for seed, count in top_seeds:
                lines.append(f"    • 种子 {seed}: 出现 {count} 次")
        
        if hypothesis['recommendations']:
            lines.append("  关键发现:")
            for rec in hypothesis['recommendations']:
                lines.append(f"    • {rec}")
        
        lines.append("=" * 80)
        return "\n".join(lines)

def main():
    """主函数"""
    finder = TrueSeedFinder("real_time_data_20250817.csv")
    result = finder.run_analysis()
    
    # 打印摘要
    print(result['summary'])
    
    # 保存详细结果
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"true_seed_analysis_{timestamp}.json"
    
    # 准备可序列化的数据
    serializable_result = {
        'summary': result['summary'],
        'hypothesis': result['hypothesis'],
        'relationship_analysis': {
            'period_count': result['relationship_analysis']['period_count'],
            'total_successful_seeds': result['relationship_analysis']['total_successful_seeds'],
            'common_patterns': result['relationship_analysis']['common_patterns'],
            'most_common_seeds': dict(result['relationship_analysis']['seed_frequency'].most_common(50)),
            'most_common_predictions': dict(result['relationship_analysis']['prediction_frequency'].most_common(10))
        }
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(serializable_result, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 详细结果已保存到: {filename}")

if __name__ == "__main__":
    main()
