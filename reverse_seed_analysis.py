#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逆向种子分析工具
基于100%准确率的滑动窗口区域，逆向分析真实的随机数种子规律
"""

import pandas as pd
import json
import ast
import random
from typing import List, Dict, Tuple, Any
from collections import defaultdict

class ReverseSeedAnalyzer:
    """逆向种子分析器"""
    
    def __init__(self, data_file: str = "real_time_data_20250817.csv"):
        """
        初始化分析器
        
        Args:
            data_file: 开奖数据文件路径
        """
        self.data_file = data_file
        self.data = None
        
        # 基于region_size_38_accuracy_report.txt的100%准确率区域
        self.perfect_regions = [
            {'region_id': 2258, 'start': 2258, 'end': 2295, 'accuracy': 100.00},
            {'region_id': 2259, 'start': 2259, 'end': 2296, 'accuracy': 100.00},
            {'region_id': 4344, 'start': 4344, 'end': 4381, 'accuracy': 100.00},
            {'region_id': 4345, 'start': 4345, 'end': 4382, 'accuracy': 100.00},
            {'region_id': 4346, 'start': 4346, 'end': 4383, 'accuracy': 100.00},
        ]
        
        self.load_data()
    
    def load_data(self):
        """加载开奖数据"""
        try:
            print(f"📂 正在加载数据文件: {self.data_file}")
            self.data = pd.read_csv(self.data_file, encoding='utf-8')
            print(f"✅ 成功加载 {len(self.data)} 条记录")
            
            # 显示数据结构
            print("\n📋 数据列信息:")
            for i, col in enumerate(self.data.columns):
                print(f"  {i}: {col}")
                
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise
    
    def parse_timestamp_sequence(self, timestamp_str: str) -> List[int]:
        """
        解析时间戳序列字符串
        
        Args:
            timestamp_str: 时间戳序列字符串
            
        Returns:
            时间戳列表
        """
        try:
            # 尝试解析为Python列表
            if timestamp_str.startswith('[') and timestamp_str.endswith(']'):
                return ast.literal_eval(timestamp_str)
            else:
                # 如果不是列表格式，尝试其他解析方式
                return [int(x.strip()) for x in timestamp_str.split(',')]
        except Exception as e:
            print(f"⚠️ 时间戳序列解析失败: {timestamp_str[:100]}... 错误: {e}")
            return []
    
    def unity_random_simulation(self, seed: int) -> int:
        """
        模拟Unity Random.Range(1, 9)
        
        Args:
            seed: 随机种子
            
        Returns:
            生成的随机数 (1-8)
        """
        random.seed(seed)
        return random.randint(1, 8)
    
    def extract_region_seeds(self, region_config: Dict) -> List[Dict]:
        """
        提取指定区域的种子数据

        Args:
            region_config: 区域配置

        Returns:
            种子数据列表
        """
        region_seeds = []
        start_idx = region_config['start']
        end_idx = region_config['end']

        print(f"\n🔍 分析区域 {region_config['region_id']} (索引 {start_idx}-{end_idx})")

        for _, row in self.data.iterrows():
            try:
                # 跳过空值或NaN
                if pd.isna(row['随机数序列对应的时间戳']) or pd.isna(row['开出的房间']):
                    continue

                # 解析时间戳序列
                timestamp_sequence = self.parse_timestamp_sequence(str(row['随机数序列对应的时间戳']))

                if len(timestamp_sequence) < end_idx + 1:
                    print(f"⚠️ 期号 {row['期号']}: 时间戳序列长度不足 ({len(timestamp_sequence)} < {end_idx + 1})")
                    continue

                # 提取区域内的种子
                region_timestamps = timestamp_sequence[start_idx:end_idx + 1]

                # 获取开出的房间号
                actual_result = int(row['开出的房间'])

                # 为每个种子生成预测
                predictions = []
                for i, timestamp in enumerate(region_timestamps):
                    predicted = self.unity_random_simulation(timestamp)
                    predictions.append({
                        'index': start_idx + i,
                        'seed': timestamp,
                        'predicted': predicted
                    })

                # 检查是否有预测命中（避开了实际结果）
                hit_predictions = [p for p in predictions if p['predicted'] != actual_result]

                region_seeds.append({
                    'period': row['期号'],
                    'draw_timestamp': row['开奖时间戳'],
                    'actual_result': actual_result,
                    'region_predictions': predictions,
                    'hit_count': len(hit_predictions),
                    'hit_predictions': hit_predictions,
                    'is_perfect_hit': len(hit_predictions) == len(predictions),  # 所有预测都避开了实际结果
                    'avoid_success_rate': len(hit_predictions) / len(predictions) * 100  # 避开成功率
                })

            except Exception as e:
                print(f"⚠️ 处理期号 {row.get('期号', 'unknown')} 失败: {e}")
                continue

        print(f"✅ 区域 {region_config['region_id']} 处理完成，有效记录: {len(region_seeds)}")
        return region_seeds
    
    def analyze_seed_patterns(self, region_seeds: List[Dict]) -> Dict:
        """
        分析种子模式
        
        Args:
            region_seeds: 区域种子数据
            
        Returns:
            分析结果
        """
        analysis = {
            'total_records': len(region_seeds),
            'perfect_hits': 0,
            'seed_frequency': defaultdict(int),
            'result_frequency': defaultdict(int),
            'seed_to_result_mapping': defaultdict(list),
            'timestamp_patterns': [],
            'successful_seeds': []  # 成功避开的种子
        }
        
        for record in region_seeds:
            if record['is_perfect_hit']:
                analysis['perfect_hits'] += 1
            
            analysis['result_frequency'][record['actual_result']] += 1
            
            # 分析每个预测
            for pred in record['region_predictions']:
                seed = pred['seed']
                predicted = pred['predicted']
                actual = record['actual_result']
                
                analysis['seed_frequency'][predicted] += 1
                analysis['seed_to_result_mapping'][seed].append({
                    'predicted': predicted,
                    'actual': actual,
                    'hit': predicted != actual,
                    'period': record['period']
                })
                
                # 如果预测成功避开了实际结果
                if predicted != actual:
                    analysis['successful_seeds'].append({
                        'seed': seed,
                        'predicted': predicted,
                        'actual': actual,
                        'period': record['period'],
                        'index': pred['index']
                    })
        
        return analysis
    
    def find_true_seed_pattern(self, all_analyses: List[Tuple[Dict, Dict]]) -> Dict:
        """
        寻找真实的种子模式
        
        Args:
            all_analyses: 所有区域的分析结果
            
        Returns:
            真实种子模式分析
        """
        print("\n🔬 开始逆向分析真实种子模式...")
        
        # 收集所有成功的种子
        all_successful_seeds = []
        for region_config, analysis in all_analyses:
            all_successful_seeds.extend(analysis['successful_seeds'])
        
        # 按期号分组
        period_seeds = defaultdict(list)
        for seed_info in all_successful_seeds:
            period_seeds[seed_info['period']].append(seed_info)
        
        # 分析种子偏移模式
        offset_patterns = defaultdict(int)
        seed_differences = []
        
        for period, seeds in period_seeds.items():
            if len(seeds) > 1:
                # 计算种子之间的差值
                seeds_sorted = sorted(seeds, key=lambda x: x['seed'])
                for i in range(1, len(seeds_sorted)):
                    diff = seeds_sorted[i]['seed'] - seeds_sorted[i-1]['seed']
                    seed_differences.append(diff)
                    offset_patterns[diff] += 1
        
        # 寻找最常见的偏移模式
        common_offsets = sorted(offset_patterns.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_successful_seeds': len(all_successful_seeds),
            'periods_analyzed': len(period_seeds),
            'common_offset_patterns': common_offsets[:10],
            'seed_differences': seed_differences,
            'average_difference': sum(seed_differences) / len(seed_differences) if seed_differences else 0,
            'period_seed_mapping': dict(period_seeds)
        }
    
    def generate_reverse_analysis_report(self, pattern_analysis: Dict, all_analyses: List) -> str:
        """
        生成逆向分析报告
        
        Args:
            pattern_analysis: 模式分析结果
            all_analyses: 所有区域分析结果
            
        Returns:
            格式化的报告字符串
        """
        report = []
        report.append("=" * 80)
        report.append("🔬 逆向种子分析报告")
        report.append("=" * 80)
        
        report.append(f"📊 分析概览:")
        report.append(f"  • 分析的100%准确率区域数: {len(all_analyses)}")
        report.append(f"  • 成功识别的种子数: {pattern_analysis['total_successful_seeds']}")
        report.append(f"  • 分析的期号数: {pattern_analysis['periods_analyzed']}")
        report.append("")
        
        report.append("🎯 各区域分析结果:")
        for i, (region_config, analysis) in enumerate(all_analyses, 1):
            report.append(f"  区域 {i}: {region_config['region_id']} (索引 {region_config['start']}-{region_config['end']})")
            report.append(f"    • 总记录数: {analysis['total_records']}")
            report.append(f"    • 完美命中数: {analysis['perfect_hits']}")
            report.append(f"    • 成功种子数: {len(analysis['successful_seeds'])}")
            report.append("")
        
        report.append("🔍 种子偏移模式分析:")
        if pattern_analysis['common_offset_patterns']:
            report.append("  最常见的种子偏移量:")
            for offset, count in pattern_analysis['common_offset_patterns']:
                report.append(f"    • 偏移 {offset}: 出现 {count} 次")
        else:
            report.append("  未发现明显的偏移模式")
        report.append("")
        
        if pattern_analysis['seed_differences']:
            report.append(f"📈 种子差值统计:")
            report.append(f"  • 平均差值: {pattern_analysis['average_difference']:.2f}")
            report.append(f"  • 差值样本数: {len(pattern_analysis['seed_differences'])}")
            report.append("")
        
        report.append("💡 关键发现:")
        report.append("  基于100%准确率区域的分析表明:")
        report.append("  1. 这些区域能够完美预测避开的号码")
        report.append("  2. 种子生成可能遵循特定的偏移模式")
        report.append("  3. 真实的随机数生成可能不是标准的Unity Random")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def run_complete_analysis(self) -> Dict:
        """
        运行完整的逆向分析
        
        Returns:
            完整分析结果
        """
        print("🚀 开始完整的逆向种子分析...")
        
        all_analyses = []
        
        # 分析每个100%准确率区域
        for region_config in self.perfect_regions:
            print(f"\n📍 分析区域 {region_config['region_id']}...")
            
            # 提取区域种子数据
            region_seeds = self.extract_region_seeds(region_config)
            
            # 分析种子模式
            analysis = self.analyze_seed_patterns(region_seeds)
            
            all_analyses.append((region_config, analysis))
            
            print(f"✅ 区域 {region_config['region_id']} 分析完成:")
            print(f"   • 处理记录: {analysis['total_records']}")
            print(f"   • 完美命中: {analysis['perfect_hits']}")
            print(f"   • 成功种子: {len(analysis['successful_seeds'])}")
        
        # 寻找真实种子模式
        pattern_analysis = self.find_true_seed_pattern(all_analyses)
        
        # 生成报告
        report = self.generate_reverse_analysis_report(pattern_analysis, all_analyses)
        print(report)
        
        # 保存详细结果
        result = {
            'perfect_regions': self.perfect_regions,
            'region_analyses': all_analyses,
            'pattern_analysis': pattern_analysis,
            'report': report
        }
        
        return result
    
    def save_analysis_results(self, results: Dict, filename: str = None):
        """
        保存分析结果到文件
        
        Args:
            results: 分析结果
            filename: 保存文件名
        """
        if filename is None:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"reverse_seed_analysis_{timestamp}.json"
        
        # 准备可序列化的数据
        serializable_results = {
            'perfect_regions': results['perfect_regions'],
            'pattern_analysis': results['pattern_analysis'],
            'report': results['report'],
            'region_summary': []
        }
        
        # 添加区域摘要（避免保存过大的数据）
        for region_config, analysis in results['region_analyses']:
            serializable_results['region_summary'].append({
                'region_config': region_config,
                'total_records': analysis['total_records'],
                'perfect_hits': analysis['perfect_hits'],
                'successful_seeds_count': len(analysis['successful_seeds'])
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"💾 分析结果已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🔬 逆向种子分析工具")
    print("基于100%准确率的滑动窗口区域进行种子逆向分析")
    print("=" * 60)
    
    try:
        # 创建分析器
        analyzer = ReverseSeedAnalyzer("real_time_data_20250817.csv")
        
        # 运行完整分析
        results = analyzer.run_complete_analysis()
        
        # 保存结果
        analyzer.save_analysis_results(results)
        
        print("\n🎉 逆向分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
