#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
避开策略验证工具
验证100%准确率滑动窗口区域的避开策略
"""

import pandas as pd
import ast
import random
from typing import List, Dict, Set

class AvoidanceStrategyValidator:
    """避开策略验证器"""
    
    def __init__(self, data_file: str = "real_time_data_20250817.csv"):
        self.data_file = data_file
        self.data = None
        
        # 100%准确率的区域（来自你的统计报告）
        self.perfect_regions = [
            {'id': 2258, 'start': 2258, 'end': 2295, 'size': 38},
            {'id': 2259, 'start': 2259, 'end': 2296, 'size': 38},
            {'id': 4344, 'start': 4344, 'end': 4381, 'size': 38},
            {'id': 4345, 'start': 4345, 'end': 4382, 'size': 38},
            {'id': 4346, 'start': 4346, 'end': 4383, 'size': 38},
        ]
        
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        print(f"📂 加载数据: {self.data_file}")
        self.data = pd.read_csv(self.data_file, encoding='utf-8')
        print(f"✅ 加载完成，共 {len(self.data)} 条记录")
    
    def parse_timestamp_list(self, timestamp_str: str) -> List[int]:
        """解析时间戳列表"""
        try:
            if pd.isna(timestamp_str):
                return []
            return ast.literal_eval(str(timestamp_str))
        except:
            return []
    
    def unity_random(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def validate_avoidance_region(self, region: Dict) -> Dict:
        """
        验证单个避开区域
        
        Args:
            region: 区域配置
            
        Returns:
            验证结果
        """
        print(f"\n🔍 验证区域 {region['id']} (索引 {region['start']}-{region['end']})")
        
        results = {
            'region_id': region['id'],
            'total_tests': 0,
            'successful_avoidances': 0,
            'accuracy': 0.0,
            'detailed_results': [],
            'avoid_predictions': []
        }
        
        for _, row in self.data.iterrows():
            try:
                if pd.isna(row['随机数序列对应的时间戳']) or pd.isna(row['开出的房间']):
                    continue
                
                period = row['期号']
                actual_result = int(row['开出的房间'])
                sequence_timestamps = self.parse_timestamp_list(row['随机数序列对应的时间戳'])
                
                if len(sequence_timestamps) <= region['end']:
                    continue
                
                # 提取区域种子
                region_seeds = sequence_timestamps[region['start']:region['end']+1]
                
                # 生成区域内所有预测
                region_predictions = []
                for seed in region_seeds:
                    predicted = self.unity_random(seed)
                    region_predictions.append(predicted)
                
                # 统计预测号码频次
                prediction_counts = {}
                for pred in region_predictions:
                    prediction_counts[pred] = prediction_counts.get(pred, 0) + 1
                
                # 确定避开的号码（出现频次最高的）
                if prediction_counts:
                    max_count = max(prediction_counts.values())
                    avoid_numbers = set(num for num, count in prediction_counts.items() 
                                      if count >= max_count * 0.7)  # 出现频次达到最高频次70%以上
                else:
                    avoid_numbers = set()
                
                # 检查避开策略是否成功
                avoidance_successful = actual_result not in avoid_numbers
                
                results['total_tests'] += 1
                if avoidance_successful:
                    results['successful_avoidances'] += 1
                
                # 记录详细结果
                result_detail = {
                    'period': period,
                    'actual_result': actual_result,
                    'avoid_numbers': sorted(list(avoid_numbers)),
                    'safe_numbers': sorted([i for i in range(1, 9) if i not in avoid_numbers]),
                    'avoidance_successful': avoidance_successful,
                    'prediction_counts': prediction_counts
                }
                
                results['detailed_results'].append(result_detail)
                results['avoid_predictions'].extend(region_predictions)
                
            except Exception as e:
                print(f"⚠️ 处理期号 {row.get('期号', 'unknown')} 失败: {e}")
                continue
        
        # 计算准确率
        if results['total_tests'] > 0:
            results['accuracy'] = results['successful_avoidances'] / results['total_tests']
        
        print(f"   测试数量: {results['total_tests']}")
        print(f"   成功避开: {results['successful_avoidances']}")
        print(f"   避开准确率: {results['accuracy']:.2%}")
        
        return results
    
    def validate_all_regions(self) -> Dict:
        """验证所有100%准确率区域"""
        print("🚀 开始验证所有100%准确率区域的避开策略...")
        
        all_results = {}
        
        for region in self.perfect_regions:
            result = self.validate_avoidance_region(region)
            all_results[region['id']] = result
        
        return all_results
    
    def analyze_combined_strategy(self, all_results: Dict) -> Dict:
        """分析组合避开策略"""
        print("\n🔬 分析组合避开策略...")
        
        # 收集所有期号的避开建议
        period_avoidance = {}
        
        for region_id, result in all_results.items():
            for detail in result['detailed_results']:
                period = detail['period']
                if period not in period_avoidance:
                    period_avoidance[period] = {
                        'actual_result': detail['actual_result'],
                        'region_avoidances': {},
                        'combined_avoid': set(),
                        'combined_safe': set(range(1, 9))
                    }
                
                period_avoidance[period]['region_avoidances'][region_id] = detail['avoid_numbers']
                period_avoidance[period]['combined_avoid'].update(detail['avoid_numbers'])
                period_avoidance[period]['combined_safe'].intersection_update(detail['safe_numbers'])
        
        # 分析组合策略效果
        combined_analysis = {
            'total_periods': len(period_avoidance),
            'conservative_success': 0,  # 保守策略：只避开所有区域都建议避开的号码
            'aggressive_success': 0,   # 激进策略：避开任一区域建议避开的号码
            'safe_choice_success': 0,  # 安全选择：选择所有区域都认为安全的号码
            'detailed_analysis': []
        }
        
        for period, data in period_avoidance.items():
            actual = data['actual_result']
            
            # 保守避开策略：所有区域都建议避开的号码
            conservative_avoid = set(range(1, 9))
            for avoid_list in data['region_avoidances'].values():
                conservative_avoid.intersection_update(avoid_list)
            
            # 激进避开策略：任一区域建议避开的号码
            aggressive_avoid = data['combined_avoid']
            
            # 安全选择策略：所有区域都认为安全的号码
            safe_choices = data['combined_safe']
            
            # 检查策略成功性
            conservative_success = actual not in conservative_avoid
            aggressive_success = actual not in aggressive_avoid
            safe_choice_success = actual in safe_choices
            
            if conservative_success:
                combined_analysis['conservative_success'] += 1
            if aggressive_success:
                combined_analysis['aggressive_success'] += 1
            if safe_choice_success:
                combined_analysis['safe_choice_success'] += 1
            
            combined_analysis['detailed_analysis'].append({
                'period': period,
                'actual': actual,
                'conservative_avoid': sorted(list(conservative_avoid)),
                'aggressive_avoid': sorted(list(aggressive_avoid)),
                'safe_choices': sorted(list(safe_choices)),
                'conservative_success': conservative_success,
                'aggressive_success': aggressive_success,
                'safe_choice_success': safe_choice_success
            })
        
        # 计算成功率
        total = combined_analysis['total_periods']
        if total > 0:
            combined_analysis['conservative_accuracy'] = combined_analysis['conservative_success'] / total
            combined_analysis['aggressive_accuracy'] = combined_analysis['aggressive_success'] / total
            combined_analysis['safe_choice_accuracy'] = combined_analysis['safe_choice_success'] / total
        
        return combined_analysis
    
    def generate_validation_report(self, all_results: Dict, combined_analysis: Dict) -> str:
        """生成验证报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("🎯 避开策略验证报告")
        lines.append("=" * 80)
        
        # 各区域验证结果
        lines.append("📊 各区域避开策略验证结果:")
        for region_id, result in all_results.items():
            lines.append(f"  区域 {region_id}:")
            lines.append(f"    • 测试数量: {result['total_tests']}")
            lines.append(f"    • 成功避开: {result['successful_avoidances']}")
            lines.append(f"    • 避开准确率: {result['accuracy']:.2%}")
            lines.append("")
        
        # 组合策略分析
        lines.append("🔄 组合避开策略分析:")
        lines.append(f"  • 分析期号数: {combined_analysis['total_periods']}")
        lines.append("")
        
        lines.append("📈 策略成功率:")
        lines.append(f"  • 保守策略 (交集避开): {combined_analysis['conservative_accuracy']:.2%}")
        lines.append(f"    成功次数: {combined_analysis['conservative_success']}/{combined_analysis['total_periods']}")
        lines.append("")
        lines.append(f"  • 激进策略 (并集避开): {combined_analysis['aggressive_accuracy']:.2%}")
        lines.append(f"    成功次数: {combined_analysis['aggressive_success']}/{combined_analysis['total_periods']}")
        lines.append("")
        lines.append(f"  • 安全选择策略: {combined_analysis['safe_choice_accuracy']:.2%}")
        lines.append(f"    成功次数: {combined_analysis['safe_choice_success']}/{combined_analysis['total_periods']}")
        lines.append("")
        
        # 策略建议
        lines.append("💡 策略建议:")
        best_accuracy = max(
            combined_analysis['conservative_accuracy'],
            combined_analysis['aggressive_accuracy'],
            combined_analysis['safe_choice_accuracy']
        )
        
        if best_accuracy >= 0.9:
            lines.append("  ✅ 发现了高效的避开策略！")
            if combined_analysis['conservative_accuracy'] == best_accuracy:
                lines.append("  🎯 推荐使用保守策略：只避开所有区域都建议的号码")
            elif combined_analysis['aggressive_accuracy'] == best_accuracy:
                lines.append("  🎯 推荐使用激进策略：避开任一区域建议的号码")
            else:
                lines.append("  🎯 推荐使用安全选择策略：选择所有区域都认为安全的号码")
        elif best_accuracy >= 0.7:
            lines.append("  ⚠️ 策略有一定效果，但还有改进空间")
        else:
            lines.append("  ❌ 避开策略效果不佳，需要重新分析")
        
        lines.append("=" * 80)
        return "\n".join(lines)
    
    def run_validation(self) -> Dict:
        """运行完整验证"""
        # 验证各个区域
        all_results = self.validate_all_regions()
        
        # 分析组合策略
        combined_analysis = self.analyze_combined_strategy(all_results)
        
        # 生成报告
        report = self.generate_validation_report(all_results, combined_analysis)
        print(report)
        
        return {
            'region_results': all_results,
            'combined_analysis': combined_analysis,
            'report': report
        }

def main():
    """主函数"""
    validator = AvoidanceStrategyValidator("real_time_data_20250817.csv")
    results = validator.run_validation()
    
    # 保存结果
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"avoidance_strategy_validation_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump({
            'report': results['report'],
            'combined_analysis': {
                'total_periods': results['combined_analysis']['total_periods'],
                'conservative_accuracy': results['combined_analysis']['conservative_accuracy'],
                'aggressive_accuracy': results['combined_analysis']['aggressive_accuracy'],
                'safe_choice_accuracy': results['combined_analysis']['safe_choice_accuracy']
            }
        }, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 验证结果已保存到: {filename}")

if __name__ == "__main__":
    main()
